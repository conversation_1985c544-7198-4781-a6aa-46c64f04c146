import { useTranslation } from "react-i18next";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Heart, Users, Award, MapPin, Calendar, Star } from "lucide-react";

const About = () => {
  const { t } = useTranslation();
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);

  const stats = [
    {
      icon: <Heart className="h-6 w-6" />,
      number: "100+",
      label: "<PERSON><PERSON><PERSON><PERSON> hàng hài lòng",
    },
    {
      icon: <Users className="h-6 w-6" />,
      number: "5",
      label: "Năm kinh nghiệm",
    },
    {
      icon: <Award className="h-6 w-6" />,
      number: "4.9",
      label: "Đánh giá trung bình",
    },
    {
      icon: <MapPin className="h-6 w-6" />,
      number: "1",
      label: "Địa điểm độc đáo",
    },
  ];

  const timeline = [
    {
      year: "2019",
      title: "Khởi đầu ý tưởng",
      description:
        "Anh Nguyễn Phát bắt đầu ấp ủ ý tưởng tạo ra không gian lưu trú gần gũi như ở nhà",
    },
    {
      year: "2020",
      title: "Nghiên cứu & Phát triển",
      description:
        "Thời gian nghiên cứu về wellness, thiết kế không gian chữa lành",
    },
    {
      year: "2021",
      title: "Xây dựng đầu tiên",
      description: "Bắt đầu xây dựng Sóng Wellness Retreat tại Đà Nẵng",
    },
    {
      year: "2022",
      title: "Khai trương",
      description:
        "Chính thức đón khách đầu tiên và nhận được phản hồi tích cực",
    },
    {
      year: "2024",
      title: "Hiện tại",
      description: "Tiếp tục phát triển và mở rộng dịch vụ wellness retreat",
    },
  ];

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1600298881974-6be191ceeda1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        >
          <div className="absolute inset-0 bg-black/50"></div>
        </div>

        <div className="relative container mx-auto px-6 text-white">
          <div className="max-w-3xl">
            <motion.p
              className="text-sm uppercase tracking-wider mb-4 text-white/90"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              VỀ CHÚNG TÔI
            </motion.p>

            <motion.h1
              className="text-5xl md:text-7xl font-light mb-6 leading-tight"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              SÓNG
              <br />
              <span className="font-normal">HOMESTAY</span>
            </motion.h1>

            <motion.div
              className="w-16 h-0.5 bg-white mb-8"
              initial={{ width: 0 }}
              animate={{ width: 64 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            />

            <motion.p
              className="text-xl md:text-2xl mb-4 text-white/90 font-light italic"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              "Chữa lành từ không gian – chạm trong từng khoảnh khắc"
            </motion.p>

            <motion.p
              className="text-lg mb-12 text-white/80"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              "Home away from home"
            </motion.p>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-2 md:grid-cols-4 gap-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-2 text-white/80">
                    {stat.icon}
                  </div>
                  <div className="text-2xl font-light text-white mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-white/70">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section ref={ref} className="py-32 px-6 relative overflow-hidden">
        {/* Background decorative elements */}
        <motion.div
          style={{ y }}
          className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full blur-3xl opacity-60"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], [-30, 30]) }}
          className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-br from-green-50 to-green-100 rounded-full blur-2xl opacity-60"
        />

        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <motion.p
                className="text-sm uppercase tracking-wider mb-4"
                style={{ color: "var(--color-text-secondary)" }}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                CÂU CHUYỆN CỦA CHÚNG TÔI
              </motion.p>

              <motion.h2
                className="text-4xl md:text-5xl font-light mb-6 leading-tight"
                style={{ color: "var(--color-text)" }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {t("about.title")}
              </motion.h2>

              <motion.div
                className="w-16 h-0.5 mb-8"
                style={{ backgroundColor: "var(--color-primary)" }}
                initial={{ width: 0 }}
                whileInView={{ width: 64 }}
                transition={{ delay: 0.5, duration: 0.8 }}
              />

              <motion.p
                className="text-lg leading-relaxed mb-8"
                style={{ color: "var(--color-text-secondary)" }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                {t("about.story")}
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <Button
                  variant="outline"
                  className="text-sm uppercase tracking-wider border-gray-300 hover:border-gray-900 transition-all duration-300 hover:scale-105"
                  style={{ color: "var(--color-text)" }}
                >
                  Tìm hiểu thêm
                </Button>
              </motion.div>
            </motion.div>

            {/* Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <motion.div
                style={{ scale }}
                className="relative overflow-hidden shadow-2xl"
              >
                <img
                  src="https://images.unsplash.com/photo-1600298881974-6be191ceeda1?w=800&h=600&fit=crop"
                  alt="Nguyen Phat - Founder"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </motion.div>

              {/* Floating quote */}
              <motion.div
                className="absolute -bottom-6 -left-6 bg-white p-6 shadow-xl max-w-xs"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1, duration: 0.6 }}
                whileHover={{ scale: 1.05 }}
              >
                <p
                  className="text-sm italic mb-2"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  "Chữa lành từ không gian – chạm trong từng khoảnh khắc"
                </p>
                <p
                  className="text-xs font-medium"
                  style={{ color: "var(--color-primary)" }}
                >
                  - Nguyễn Phát, Founder
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-32 px-6 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <p
              className="text-sm uppercase tracking-wider mb-4"
              style={{ color: "var(--color-text-secondary)" }}
            >
              HÀNH TRÌNH PHÁT TRIỂN
            </p>
            <h2
              className="text-4xl md:text-5xl font-light mb-6 leading-tight"
              style={{ color: "var(--color-text)" }}
            >
              Câu chuyện thời gian
            </h2>
            <div
              className="w-16 h-0.5 mx-auto"
              style={{ backgroundColor: "var(--color-primary)" }}
            />
          </motion.div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gray-300"></div>

            {timeline.map((item, index) => (
              <motion.div
                key={index}
                className={`relative flex items-center mb-16 ${
                  index % 2 === 0 ? "flex-row" : "flex-row-reverse"
                }`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div
                  className={`w-1/2 ${
                    index % 2 === 0 ? "pr-8 text-right" : "pl-8 text-left"
                  }`}
                >
                  <motion.div
                    className="bg-white p-6 shadow-lg rounded-lg"
                    whileHover={{ y: -5, scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div
                      className="text-2xl font-light mb-2"
                      style={{ color: "var(--color-primary)" }}
                    >
                      {item.year}
                    </div>
                    <h3
                      className="text-xl font-medium mb-3"
                      style={{ color: "var(--color-text)" }}
                    >
                      {item.title}
                    </h3>
                    <p
                      className="text-sm leading-relaxed"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      {item.description}
                    </p>
                  </motion.div>
                </div>

                {/* Timeline dot */}
                <motion.div
                  className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full border-4 border-white shadow-lg"
                  style={{ backgroundColor: "var(--color-primary)" }}
                  whileHover={{ scale: 1.5 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Current Project Section */}
      <section className="py-32 px-6">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.p
              className="text-sm uppercase tracking-wider mb-4"
              style={{ color: "var(--color-text-secondary)" }}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {t("about.currentProject")}
            </motion.p>

            <motion.h3
              className="text-3xl md:text-4xl font-light mb-6"
              style={{ color: "var(--color-primary)" }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {t("about.projectName")}
            </motion.h3>

            <motion.p
              className="text-lg max-w-2xl mx-auto leading-relaxed"
              style={{ color: "var(--color-text-secondary)" }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              {t("about.projectDescription")}
            </motion.p>
          </motion.div>

          {/* Project Gallery */}
          <div className="grid md:grid-cols-3 gap-8">
            {[
              "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
              "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
              "https://images.unsplash.com/photo-1545389336-cf090694435e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            ].map((image, index) => (
              <motion.div
                key={index}
                className="relative overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
              >
                <img
                  src={image}
                  alt={`Project ${index + 1}`}
                  className="w-full h-64 object-cover"
                />
                <div className="absolute inset-0 bg-black/20 hover:bg-black/10 transition-colors duration-300" />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
