import Header from "@/components/Header";

const About = () => {
  const values = [
    {
      title: "Wellness First",
      description: "Every design decision is made with health and well-being in mind, creating spaces that nurture both body and soul."
    },
    {
      title: "Sustainable Living",
      description: "We incorporate eco-friendly materials and energy-efficient systems to minimize our environmental impact."
    },
    {
      title: "Balinese Heritage",
      description: "Our designs honor traditional Balinese architecture while embracing modern luxury and functionality."
    },
    {
      title: "Quality Craftsmanship",
      description: "We work with master craftsmen and premium materials to ensure lasting beauty and durability."
    }
  ];

  const team = [
    {
      name: "<PERSON> Sutrisna",
      role: "Founder & CEO",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face"
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b1e5?w=400&h=400&fit=crop&crop=face"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Master Builder",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-light mb-6">
              ABOUT <span className="font-medium">KENKO</span>
            </h1>
            <div className="text-2xl font-light text-gray-600 mb-8">健康</div>
            <p className="text-xl text-gray-600">
              Founded on the Japanese principle of "Kenko" (健康) meaning health and wellness, 
              we create luxury properties that prioritize your well-being while celebrating Bali's natural beauty.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-light mb-8">Our Story</h2>
              <div className="space-y-6 text-gray-700 leading-relaxed">
                <p>
                  KENKO was born from a vision to create spaces where luxury meets wellness, 
                  where every element is designed to enhance your quality of life. Our founder, 
                  inspired by years living in both Japan and Bali, recognized the need for 
                  properties that truly support holistic well-being.
                </p>
                <p>
                  We believe that your home should be your sanctuary – a place that energizes 
                  you, calms you, and supports your health goals. This philosophy guides every 
                  aspect of our design process, from the selection of natural materials to the 
                  integration of wellness amenities.
                </p>
                <p>
                  Today, KENKO stands as Bali's premier wellness-focused property developer, 
                  having created over 50 luxury villas that embody our commitment to health, 
                  sustainability, and exceptional design.
                </p>
              </div>
            </div>
            <div>
              <img 
                src="https://images.unsplash.com/photo-1600298881974-6be191ceeda1?w=800&h=600&fit=crop" 
                alt="KENKO founder"
                className="w-full h-96 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-light mb-6">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              These core principles guide everything we do, ensuring that every KENKO property 
              reflects our commitment to excellence and well-being.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-medium mb-4">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-light mb-6">Meet Our Team</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our diverse team combines international expertise with deep local knowledge 
              to create truly exceptional properties.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {team.map((member, index) => (
              <div key={index} className="text-center">
                <img 
                  src={member.image} 
                  alt={member.name}
                  className="w-48 h-48 object-cover rounded-full mx-auto mb-6"
                />
                <h3 className="text-xl font-medium mb-2">{member.name}</h3>
                <p className="text-gray-600">{member.role}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-light mb-6">Ready to Build Your Wellness Sanctuary?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can create the perfect wellness-focused property for you.
          </p>
          <button className="bg-white text-black px-8 py-3 hover:bg-gray-100 transition-colors">
            Start Your Journey
          </button>
        </div>
      </section>
    </div>
  );
};

export default About;