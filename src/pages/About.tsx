import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import Header from "@/components/Header";

const About = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section
          className="py-20 px-6"
          style={{ backgroundColor: "var(--color-accent)" }}
        >
          <div className="container mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1
                className="text-5xl md:text-6xl font-bold mb-6"
                style={{ color: "var(--color-primary)" }}
              >
                SÓNG
              </h1>
              <p
                className="text-2xl md:text-3xl mb-4"
                style={{ color: "var(--color-text)" }}
              >
                Homestay
              </p>
              <p
                className="text-xl italic mb-8"
                style={{ color: "var(--color-text-secondary)" }}
              >
                "Chữa lành từ không gian – chạm trong từng khoảnh khắc"
              </p>
              <p
                className="text-lg"
                style={{ color: "var(--color-text-secondary)" }}
              >
                "Home away from home"
              </p>
            </motion.div>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-20 px-6">
          <div className="container mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2
                  className="text-4xl font-bold mb-6"
                  style={{ color: "var(--color-primary)" }}
                >
                  {t("about.title")}
                </h2>
                <div
                  className="w-16 h-1 mb-6"
                  style={{ backgroundColor: "var(--color-primary)" }}
                ></div>
                <p
                  className="text-lg leading-relaxed"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  {t("about.story")}
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <img
                  src="/placeholder.svg"
                  alt="Nguyen Phat - Founder"
                  className="w-full h-96 object-cover rounded-lg shadow-lg"
                />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Current Project Section */}
        <section
          className="py-20 px-6"
          style={{ backgroundColor: "var(--color-accent)" }}
        >
          <div className="container mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2
                className="text-4xl font-bold mb-6"
                style={{ color: "var(--color-text)" }}
              >
                {t("about.currentProject")}
              </h2>
              <div
                className="w-24 h-1 mx-auto mb-8"
                style={{ backgroundColor: "var(--color-primary)" }}
              ></div>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <img
                  src="/placeholder.svg"
                  alt="Song Wellness Retreat"
                  className="w-full h-96 object-cover rounded-lg shadow-lg"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h3
                  className="text-3xl font-bold mb-6"
                  style={{ color: "var(--color-primary)" }}
                >
                  {t("about.projectName")}
                </h3>
                <p
                  className="text-lg leading-relaxed"
                  style={{ color: "var(--color-text-secondary)" }}
                >
                  {t("about.projectDescription")}
                </p>
              </motion.div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default About;
