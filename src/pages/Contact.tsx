import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import Header from "@/components/Header";
import { Button } from "@/components/ui/button";
import { Phone, Mail, MessageCircle, Instagram, Facebook } from "lucide-react";

const Contact = () => {
  const { t } = useTranslation();

  const contactInfo = [
    {
      icon: <Phone className="h-6 w-6" />,
      label: t("contact.phone"),
      value: "0911.388.703",
      link: "tel:0911388703",
    },
    {
      icon: <Mail className="h-6 w-6" />,
      label: t("contact.email"),
      value: "<EMAIL>",
      link: "mailto:<EMAIL>",
    },
  ];

  const socialLinks = [
    {
      name: "Facebook",
      icon: <Facebook className="h-6 w-6" />,
      link: "https://facebook.com/songhomestay",
      username: "<PERSON><PERSON><PERSON>y",
    },
    {
      name: "Instagram",
      icon: <Instagram className="h-6 w-6" />,
      link: "https://instagram.com/_songhomestay",
      username: "_songhomestay",
    },
  ];

  const qrContacts = [
    {
      name: "WhatsApp",
      qrCode: "/placeholder.svg", // Replace with actual QR code
      number: "0911.388.703",
    },
    {
      name: "Zalo",
      qrCode: "/placeholder.svg", // Replace with actual QR code
      number: "0911.388.703",
    },
    {
      name: "KakaoTalk",
      qrCode: "/placeholder.svg", // Replace with actual QR code
      username: "_shelby",
    },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section
          className="py-20 px-6"
          style={{ backgroundColor: "var(--color-accent)" }}
        >
          <div className="container mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1
                className="text-5xl md:text-6xl font-bold mb-6"
                style={{ color: "var(--color-primary)" }}
              >
                {t("contact.title")}
              </h1>
              <p
                className="text-xl"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Hãy liên hệ với chúng tôi để bắt đầu hành trình trải nghiệm của
                bạn
              </p>
            </motion.div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-20 px-6">
          <div className="container mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Basic Contact Info */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-lg shadow-lg"
              >
                <h3
                  className="text-2xl font-bold mb-6"
                  style={{ color: "var(--color-primary)" }}
                >
                  Thông tin liên hệ
                </h3>
                <div className="space-y-4">
                  {contactInfo.map((info, index) => (
                    <a
                      key={index}
                      href={info.link}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div style={{ color: "var(--color-primary)" }}>
                        {info.icon}
                      </div>
                      <div>
                        <p
                          className="text-sm"
                          style={{ color: "var(--color-text-secondary)" }}
                        >
                          {info.label}
                        </p>
                        <p
                          className="font-medium"
                          style={{ color: "var(--color-text)" }}
                        >
                          {info.value}
                        </p>
                      </div>
                    </a>
                  ))}
                </div>
              </motion.div>

              {/* Social Media */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-lg shadow-lg"
              >
                <h3
                  className="text-2xl font-bold mb-6"
                  style={{ color: "var(--color-primary)" }}
                >
                  {t("contact.social")}
                </h3>
                <div className="space-y-4">
                  {socialLinks.map((social, index) => (
                    <a
                      key={index}
                      href={social.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div style={{ color: "var(--color-primary)" }}>
                        {social.icon}
                      </div>
                      <div>
                        <p
                          className="text-sm"
                          style={{ color: "var(--color-text-secondary)" }}
                        >
                          {social.name}
                        </p>
                        <p
                          className="font-medium"
                          style={{ color: "var(--color-text)" }}
                        >
                          {social.username}
                        </p>
                      </div>
                    </a>
                  ))}
                </div>
              </motion.div>

              {/* QR Codes */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-lg shadow-lg"
              >
                <h3
                  className="text-2xl font-bold mb-6"
                  style={{ color: "var(--color-primary)" }}
                >
                  QR Codes
                </h3>
                <div className="space-y-6">
                  {qrContacts.map((contact, index) => (
                    <div key={index} className="text-center">
                      <p
                        className="font-medium mb-2"
                        style={{ color: "var(--color-text)" }}
                      >
                        {contact.name}
                      </p>
                      <img
                        src={contact.qrCode}
                        alt={`${contact.name} QR Code`}
                        className="w-24 h-24 mx-auto mb-2 border rounded"
                      />
                      <p
                        className="text-sm"
                        style={{ color: "var(--color-text-secondary)" }}
                      >
                        {contact.number || contact.username}
                      </p>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section
          className="py-20 px-6"
          style={{ backgroundColor: "var(--color-primary)" }}
        >
          <div className="container mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold text-white mb-6">
                Sẵn sàng cho hành trình chữa lành?
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                Liên hệ với chúng tôi ngay hôm nay để đặt gói trải nghiệm phù
                hợp với bạn
              </p>
              <Button
                size="lg"
                className="px-12 py-4 text-lg font-semibold bg-white hover:bg-gray-100"
                style={{ color: "var(--color-primary)" }}
              >
                {t("nav.bookPackage")}
              </Button>
            </motion.div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Contact;
