import Header from "@/components/Header";
import { Badge } from "@/components/ui/badge";

const Projects = () => {
  const projects = [
    {
      id: 1,
      title: "KENKO VILLA CANGGU",
      location: "Canggu, Bali",
      image: "https://images.unsplash.com/photo-1602391833977-358a52198938?w=800&h=600&fit=crop",
      status: "SOLD OUT",
      bedrooms: 4,
      bathrooms: 5,
      area: "450 sqm"
    },
    {
      id: 2,
      title: "KENKO VILLA SEMINYAK",
      location: "Seminyak, Bali",
      image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop",
      status: "SOLD OUT",
      bedrooms: 5,
      bathrooms: 6,
      area: "520 sqm"
    },
    {
      id: 3,
      title: "KENKO VILLA UBUD",
      location: "Ubud, Bali",
      image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop",
      status: "AVAILABLE",
      bedrooms: 3,
      bathrooms: 4,
      area: "380 sqm"
    },
    {
      id: 4,
      title: "KENKO VILLA ULUWATU",
      location: "Uluwatu, Bali",
      image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop",
      status: "COMING SOON",
      bedrooms: 6,
      bathrooms: 7,
      area: "600 sqm"
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-light mb-6">
              OUR <span className="font-medium">PROJECTS</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Discover our collection of luxury wellness villas designed for modern living and holistic well-being.
            </p>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-12">
            {projects.map((project) => (
              <div key={project.id} className="group cursor-pointer">
                <div className="relative overflow-hidden mb-6">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge 
                      variant={project.status === "SOLD OUT" ? "destructive" : 
                               project.status === "AVAILABLE" ? "default" : "secondary"}
                      className="px-3 py-1"
                    >
                      {project.status}
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h3 className="text-2xl font-light">{project.title}</h3>
                  <p className="text-gray-600">{project.location}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span>{project.bedrooms} Bedrooms</span>
                    <span>{project.bathrooms} Bathrooms</span>
                    <span>{project.area}</span>
                  </div>
                  
                  <div className="pt-4">
                    <button className="text-black hover:text-gray-600 transition-colors font-medium">
                      View Details →
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-light mb-6">Ready to Invest in Wellness?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Contact our team to learn more about our upcoming projects and investment opportunities.
          </p>
          <button className="bg-white text-black px-8 py-3 hover:bg-gray-100 transition-colors">
            Contact Us
          </button>
        </div>
      </section>
    </div>
  );
};

export default Projects;