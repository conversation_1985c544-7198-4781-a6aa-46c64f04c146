import Header from "@/components/Header";
import { Badge } from "@/components/ui/badge";

const Blog = () => {
  const featuredPost = {
    title: "The Science Behind Wellness Architecture",
    excerpt: "Discover how thoughtful design can significantly impact your physical and mental well-being, from natural light optimization to air quality management.",
    image: "https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?w=800&h=500&fit=crop",
    category: "Wellness",
    readTime: "8 min read",
    date: "December 15, 2024"
  };

  const posts = [
    {
      title: "Sustainable Building Materials in Tropical Climates",
      excerpt: "Exploring eco-friendly construction materials that thrive in Bali's humid environment while maintaining luxury standards.",
      image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=400&h=300&fit=crop",
      category: "Sustainability",
      readTime: "6 min read",
      date: "December 10, 2024"
    },
    {
      title: "Creating Your Perfect Meditation Space",
      excerpt: "Design principles for crafting a tranquil meditation area that promotes mindfulness and inner peace.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
      category: "Design",
      readTime: "5 min read",
      date: "December 5, 2024"
    },
    {
      title: "The Benefits of Biophilic Design",
      excerpt: "How incorporating natural elements into your living space can reduce stress and improve overall health.",
      image: "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=300&fit=crop",
      category: "Wellness",
      readTime: "7 min read",
      date: "November 28, 2024"
    },
    {
      title: "Investment Opportunities in Bali Real Estate",
      excerpt: "Understanding the current market trends and future prospects for luxury property investment in Bali.",
      image: "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=300&fit=crop",
      category: "Investment",
      readTime: "10 min read",
      date: "November 20, 2024"
    },
    {
      title: "Traditional Balinese Architecture Meets Modern Luxury",
      excerpt: "How we honor local building traditions while incorporating contemporary amenities and design elements.",
      image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop",
      category: "Design",
      readTime: "6 min read",
      date: "November 15, 2024"
    },
    {
      title: "Wellness Amenities That Add Real Value",
      excerpt: "From infinity pools to yoga pavilions, discover which wellness features provide the best return on investment.",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
      category: "Wellness",
      readTime: "8 min read",
      date: "November 8, 2024"
    }
  ];

  const categories = ["All", "Wellness", "Design", "Sustainability", "Investment"];

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-light mb-6">
              KENKO <span className="font-medium">JOURNAL</span>
            </h1>
            <p className="text-xl text-gray-600">
              Insights on wellness architecture, sustainable design, and the art of conscious living.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <Badge className="mb-4">{featuredPost.category}</Badge>
                <h2 className="text-4xl font-light mb-6">{featuredPost.title}</h2>
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  {featuredPost.excerpt}
                </p>
                <div className="flex items-center text-sm text-gray-500 mb-6">
                  <span>{featuredPost.date}</span>
                  <span className="mx-2">•</span>
                  <span>{featuredPost.readTime}</span>
                </div>
                <button className="text-black hover:text-gray-600 transition-colors font-medium">
                  Read Full Article →
                </button>
              </div>
              <div>
                <img 
                  src={featuredPost.image} 
                  alt={featuredPost.title}
                  className="w-full h-80 object-cover rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 border-y border-gray-200">
        <div className="container mx-auto px-6">
          <div className="flex justify-center space-x-8">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 text-sm hover:text-black transition-colors border-b-2 border-transparent hover:border-black"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {posts.map((post, index) => (
              <article key={index} className="group cursor-pointer">
                <div className="overflow-hidden rounded-lg mb-6">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                
                <div className="space-y-3">
                  <Badge variant="outline">{post.category}</Badge>
                  <h3 className="text-xl font-light group-hover:text-gray-600 transition-colors">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center text-xs text-gray-500">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>{post.readTime}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-light mb-6">Stay Updated</h2>
            <p className="text-gray-600 mb-8">
              Subscribe to our newsletter for the latest insights on wellness architecture, 
              design trends, and exclusive property updates.
            </p>
            <div className="flex max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-black"
              />
              <button className="bg-black text-white px-6 py-3 rounded-r-md hover:bg-gray-800 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;