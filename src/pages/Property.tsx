import { useTranslation } from 'react-i18next';
import Header from "@/components/Header";
import { Button } from "@/components/ui/button";

const Property = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        <div className="container mx-auto px-6 py-12">
          <h1 className="text-4xl font-bold text-center mb-12" style={{ color: 'var(--color-text)' }}>
            {t('property.title')}
          </h1>
          
          <div className="grid md:grid-cols-2 gap-8 items-center">
            {/* Property Image */}
            <div className="relative">
              <img 
                src="/placeholder.svg" 
                alt="Song Homestay Property"
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
            
            {/* Property Information */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold" style={{ color: 'var(--color-primary)' }}>
                Sóng Wellness Retreat
              </h2>
              
              <p className="text-lg" style={{ color: 'var(--color-text-secondary)' }}>
                Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc, dành cho những ai tìm về sự an yên và kết nối bên trong.
              </p>
              
              <div className="space-y-4">
                <h3 className="text-xl font-medium" style={{ color: 'var(--color-text)' }}>
                  Tiện ích & Đặc điểm nổi bật:
                </h3>
                <ul className="space-y-2" style={{ color: 'var(--color-text-secondary)' }}>
                  <li>• Vị trí gần biển, view tuyệt đẹp</li>
                  <li>• Không gian thiết kế hiện đại, gần gũi với thiên nhiên</li>
                  <li>• Khu vực spa và trị liệu chuyên nghiệp</li>
                  <li>• Phòng meditation và yoga</li>
                  <li>• Khu vườn thảo dược và thư giãn</li>
                </ul>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  className="flex-1"
                  style={{ 
                    backgroundColor: 'var(--color-primary)',
                    color: 'white'
                  }}
                >
                  {t('property.moreInfo')}
                </Button>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium" style={{ color: 'var(--color-text)' }}>
                  {t('property.bookOn')}:
                </h4>
                <div className="flex flex-wrap gap-3">
                  <Button variant="outline" size="sm">
                    Airbnb
                  </Button>
                  <Button variant="outline" size="sm">
                    Agoda
                  </Button>
                  <Button variant="outline" size="sm">
                    Booking.com
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Property;
