import { useTranslation } from "react-i18next";
import { motion, useScroll, useTransform } from "framer-motion";
import { useState, useRef } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Wifi,
  Car,
  Coffee,
  Waves,
  TreePine,
  Star,
  Users,
  Bed,
  Bath,
} from "lucide-react";

const Property = () => {
  const { t } = useTranslation();
  const [selectedImage, setSelectedImage] = useState(0);
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);

  // Images from Google Drive and high-quality stock photos
  const propertyImages = [
    "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Main hero - Modern villa exterior
    "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Bedroom with natural light
    "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Modern bathroom
    "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Spa/wellness area
    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Garden/outdoor space
    "https://images.unsplash.com/photo-1545389336-cf090694435e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Pool area
    "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Kitchen/dining
    "https://images.unsplash.com/photo-1600298881974-6be191ceeda1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Living room
    "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Meditation space
    "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Terrace/balcony
    "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Interior detail
    "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80", // Wellness corner
  ];

  const amenities = [
    { icon: <Waves className="h-5 w-5" />, name: "Spa & Wellness Center" },
    { icon: <TreePine className="h-5 w-5" />, name: "Private Garden" },
    { icon: <Wifi className="h-5 w-5" />, name: "High-Speed WiFi" },
    { icon: <Car className="h-5 w-5" />, name: "Free Parking" },
    { icon: <Coffee className="h-5 w-5" />, name: "Fully Equipped Kitchen" },
    { icon: <MapPin className="h-5 w-5" />, name: "Beach Access (5 min)" },
  ];

  const features = [
    { icon: <Bed className="h-5 w-5" />, label: "Bedrooms", value: "3" },
    { icon: <Bath className="h-5 w-5" />, label: "Bathrooms", value: "2" },
    { icon: <Users className="h-5 w-5" />, label: "Guests", value: "6" },
    { icon: <Star className="h-5 w-5" />, label: "Rating", value: "4.9" },
  ];

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('${propertyImages[0]}')`,
          }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        <div className="relative container mx-auto px-6 text-white">
          <div className="max-w-2xl">
            <motion.p
              className="text-sm uppercase tracking-wider mb-4 text-white/90"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              SÓNG WELLNESS RETREAT
            </motion.p>

            <motion.h1
              className="text-5xl md:text-6xl font-light mb-6 leading-tight"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              KHÔNG GIAN
              <br />
              <span className="font-normal">CHỮA LÀNH</span>
            </motion.h1>

            <motion.div
              className="w-16 h-0.5 bg-white mb-8"
              initial={{ width: 0 }}
              animate={{ width: 64 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            />

            <motion.p
              className="text-lg mb-8 text-white/90 max-w-lg leading-relaxed"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc, dành cho
              những ai tìm về sự an yên và kết nối bên trong.
            </motion.p>

            <motion.div
              className="flex flex-wrap gap-4"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full"
                >
                  <div className="text-white/80">{feature.icon}</div>
                  <span className="text-white/90 text-sm">
                    {feature.value} {feature.label}
                  </span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Image Gallery Section */}
      <section
        ref={ref}
        className="py-32 px-6 bg-gray-50 relative overflow-hidden"
      >
        <motion.div
          style={{ y }}
          className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full blur-3xl opacity-60"
        />

        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <p
              className="text-sm uppercase tracking-wider mb-4"
              style={{ color: "var(--color-text-secondary)" }}
            >
              KHÁM PHÁ KHÔNG GIAN
            </p>
            <h2
              className="text-4xl md:text-5xl font-light mb-6 leading-tight"
              style={{ color: "var(--color-text)" }}
            >
              Thư viện hình ảnh
            </h2>
            <div
              className="w-16 h-0.5 mx-auto"
              style={{ backgroundColor: "var(--color-primary)" }}
            />
          </motion.div>

          {/* Main Gallery */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Large Featured Image */}
            <motion.div
              className="lg:col-span-2"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="relative h-96 lg:h-[500px] overflow-hidden shadow-2xl cursor-pointer"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <img
                  src={propertyImages[selectedImage]}
                  alt={`Sóng Homestay - Image ${selectedImage + 1}`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                <div className="absolute bottom-4 left-4 text-white">
                  <p className="text-sm opacity-80">
                    {selectedImage + 1} / {propertyImages.length}
                  </p>
                </div>
              </motion.div>
            </motion.div>

            {/* Thumbnail Grid */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="grid grid-cols-2 lg:grid-cols-1 gap-4">
                {propertyImages.slice(0, 8).map((image, index) => (
                  <motion.div
                    key={index}
                    className={`relative h-24 lg:h-20 overflow-hidden cursor-pointer shadow-lg transition-all duration-300 ${
                      selectedImage === index
                        ? "ring-2 ring-offset-2 ring-blue-500"
                        : "hover:shadow-xl"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <div
                      className={`absolute inset-0 transition-all duration-300 ${
                        selectedImage === index
                          ? "bg-blue-500/20"
                          : "bg-black/20 hover:bg-black/10"
                      }`}
                    />
                    {selectedImage === index && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full shadow-lg" />
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>

              {propertyImages.length > 8 && (
                <motion.button
                  className="w-full h-20 bg-gray-200 hover:bg-gray-300 transition-colors duration-300 flex items-center justify-center text-gray-600 font-medium rounded-lg"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    // Cycle through remaining images
                    const nextIndex =
                      (selectedImage + 1) % propertyImages.length;
                    setSelectedImage(nextIndex);
                  }}
                >
                  +{propertyImages.length - 8} ảnh khác
                </motion.button>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Full Gallery Grid Section */}
      <section className="py-32 px-6">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <p
              className="text-sm uppercase tracking-wider mb-4"
              style={{ color: "var(--color-text-secondary)" }}
            >
              TOÀN BỘ KHÔNG GIAN
            </p>
            <h2
              className="text-4xl md:text-5xl font-light mb-6 leading-tight"
              style={{ color: "var(--color-text)" }}
            >
              Khám phá từng góc nhỏ
            </h2>
            <div
              className="w-16 h-0.5 mx-auto"
              style={{ backgroundColor: "var(--color-primary)" }}
            />
          </motion.div>

          {/* Masonry-style Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {propertyImages.map((image, index) => (
              <motion.div
                key={index}
                className={`relative overflow-hidden cursor-pointer shadow-lg hover:shadow-2xl transition-all duration-500 ${
                  index % 7 === 0
                    ? "md:col-span-2 md:row-span-2"
                    : index % 5 === 0
                    ? "lg:col-span-2"
                    : ""
                }`}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02 }}
                onClick={() => setSelectedImage(index)}
              >
                <div
                  className={`relative ${
                    index % 7 === 0
                      ? "h-64 md:h-80"
                      : index % 5 === 0
                      ? "h-48"
                      : "h-40"
                  }`}
                >
                  <img
                    src={image}
                    alt={`Sóng Homestay Gallery ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/20 hover:bg-black/10 transition-colors duration-300" />

                  {/* Overlay with image number */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/20 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                      {index + 1}
                    </span>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-full text-sm font-medium">
                      Xem chi tiết
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* View All Button */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <Button
              variant="outline"
              size="lg"
              className="px-12 py-4 text-lg font-semibold border-gray-300 hover:border-gray-900 transition-all duration-300 hover:scale-105"
              style={{ color: "var(--color-text)" }}
            >
              Xem thêm hình ảnh
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Property Details Section */}
      <section className="py-32 px-6">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Property Information */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <p
                className="text-sm uppercase tracking-wider mb-4"
                style={{ color: "var(--color-text-secondary)" }}
              >
                CHI TIẾT PROPERTY
              </p>

              <h3
                className="text-3xl md:text-4xl font-light mb-6 leading-tight"
                style={{ color: "var(--color-primary)" }}
              >
                Sóng Wellness Retreat
              </h3>

              <div
                className="w-16 h-0.5 mb-8"
                style={{ backgroundColor: "var(--color-primary)" }}
              />

              <p
                className="text-lg leading-relaxed mb-8"
                style={{ color: "var(--color-text-secondary)" }}
              >
                Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc, dành cho
                những ai tìm về sự an yên và kết nối bên trong. Với thiết kế
                hiện đại hòa quyện cùng thiên nhiên, mỗi góc nhỏ đều được chăm
                chút tỉ mỉ để mang lại cảm giác thư thái tuyệt đối.
              </p>

              {/* Features Grid */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    className="text-center p-4 bg-gray-50 rounded-lg"
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div
                      className="flex justify-center mb-2"
                      style={{ color: "var(--color-primary)" }}
                    >
                      {feature.icon}
                    </div>
                    <p
                      className="text-2xl font-light mb-1"
                      style={{ color: "var(--color-text)" }}
                    >
                      {feature.value}
                    </p>
                    <p
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      {feature.label}
                    </p>
                  </motion.div>
                ))}
              </div>

              {/* Amenities */}
              <div className="mb-8">
                <h4
                  className="text-xl font-medium mb-6"
                  style={{ color: "var(--color-text)" }}
                >
                  Tiện ích & Đặc điểm nổi bật:
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  {amenities.map((amenity, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div style={{ color: "var(--color-primary)" }}>
                        {amenity.icon}
                      </div>
                      <span style={{ color: "var(--color-text-secondary)" }}>
                        {amenity.name}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Booking Section */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:sticky lg:top-24"
            >
              <div className="bg-white p-8 shadow-2xl rounded-lg border">
                <h4
                  className="text-2xl font-light mb-6"
                  style={{ color: "var(--color-text)" }}
                >
                  Đặt phòng ngay
                </h4>

                <div className="space-y-6">
                  {/* Booking Platforms */}
                  <div>
                    <p
                      className="text-sm font-medium mb-4"
                      style={{ color: "var(--color-text)" }}
                    >
                      {t("property.bookOn")}:
                    </p>
                    <div className="space-y-3">
                      {[
                        { name: "Airbnb", url: "#", color: "bg-red-500" },
                        { name: "Agoda", url: "#", color: "bg-blue-500" },
                        { name: "Booking.com", url: "#", color: "bg-blue-600" },
                      ].map((platform, index) => (
                        <motion.a
                          key={index}
                          href={platform.url}
                          className={`block w-full ${platform.color} text-white text-center py-3 rounded-lg font-medium hover:opacity-90 transition-opacity`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          Đặt trên {platform.name}
                        </motion.a>
                      ))}
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Button
                        className="w-full py-4 text-lg font-semibold text-white hover:opacity-90"
                        style={{ backgroundColor: "var(--color-primary)" }}
                      >
                        {t("property.moreInfo")}
                      </Button>
                    </motion.div>
                  </div>

                  <div className="text-center">
                    <p
                      className="text-sm"
                      style={{ color: "var(--color-text-secondary)" }}
                    >
                      Hoặc liên hệ trực tiếp:
                      <a
                        href="tel:0911388703"
                        className="font-medium ml-1"
                        style={{ color: "var(--color-primary)" }}
                      >
                        0911.388.703
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Property;
