import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  vi: {
    translation: {
      nav: {
        home: "Trang chủ",
        about: "Về chúng tôi", 
        property: "Chỗ nghỉ",
        contact: "Liên hệ",
        bookPackage: "Đặt gói trải nghiệm"
      },
      hero: {
        title: "Chữa lành từ không gian – chạm trong từng khoảnh khắc",
        subtitle: "Home away from home",
        description: "Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc, dành cho những ai tìm về sự an yên và kết nối bên trong.",
        bookNow: "Đặt ngay gói trải nghiệm"
      },
      packages: {
        title: "Gói trải nghiệm được thiết kế riêng",
        spa: {
          title: "Spa & Trị liệu sức khỏe",
          description: "<PERSON>h<PERSON> giãn và phục hồi năng lượng với các liệu pháp spa chuyên nghiệp"
        },
        culture: {
          title: "Trải nghiệm văn hóa địa phương", 
          description: "Khám phá và trải nghiệm văn hóa bản địa đặc sắc"
        },
        emotional: {
          title: "Hỗ trợ cảm xúc – Hành trình quay về bên trong",
          description: "Tìm lại sự cân bằng và kết nối với chính mình"
        }
      },
      about: {
        title: "Câu chuyện thương hiệu",
        story: "Sóng Homestay được sáng lập tại Đà Nẵng bởi anh Nguyễn Phát – người mang trong mình mong muốn tạo ra không gian lưu trú gần gũi như ở nhà và chan hòa với thiên nhiên biển cả. Chúng tôi tin rằng mỗi homestay không chỉ là nơi nghỉ dưỡng mà còn là một hành trình trải nghiệm cảm xúc, một \"ngôi nhà thứ hai\" cho bạn.",
        currentProject: "Dự án hiện tại",
        projectName: "Sóng – Wellness Retreat",
        projectDescription: "Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc, dành cho những ai tìm về sự an yên và kết nối bên trong."
      },
      property: {
        title: "Chỗ nghỉ của chúng tôi",
        moreInfo: "Thông tin chi tiết",
        bookOn: "Đặt phòng trên"
      },
      contact: {
        title: "Liên hệ với chúng tôi",
        phone: "Số điện thoại",
        email: "Email",
        social: "Mạng xã hội"
      }
    }
  },
  en: {
    translation: {
      nav: {
        home: "Home",
        about: "About Us",
        property: "Our Property", 
        contact: "Contact",
        bookPackage: "Book a Package"
      },
      hero: {
        title: "Healing through space – touching every moment",
        subtitle: "Home away from home",
        description: "A wellness retreat space with healing and emotional elements, for those seeking inner peace and connection.",
        bookNow: "Book a Package Now"
      },
      packages: {
        title: "Customized Experience Packages",
        spa: {
          title: "Spa & Health Therapy",
          description: "Relax and restore energy with professional spa treatments"
        },
        culture: {
          title: "Local Cultural Experience",
          description: "Discover and experience unique local culture"
        },
        emotional: {
          title: "Emotional Support – Inner Journey",
          description: "Find balance and reconnect with yourself"
        }
      },
      about: {
        title: "Our Story",
        story: "Song Homestay was founded in Da Nang by Mr. Nguyen Phat – someone who carries the desire to create accommodation spaces that are as close as home and harmonious with the ocean nature. We believe that each homestay is not only a place to rest but also an emotional experience journey, a \"second home\" for you.",
        currentProject: "Current Project",
        projectName: "Song – Wellness Retreat",
        projectDescription: "A retreat space with healing and emotional elements, for those seeking inner peace and connection."
      },
      property: {
        title: "Our Property",
        moreInfo: "More Information",
        bookOn: "Book on"
      },
      contact: {
        title: "Contact Us",
        phone: "Phone Number",
        email: "Email",
        social: "Social Media"
      }
    }
  },
  ko: {
    translation: {
      nav: {
        home: "홈",
        about: "소개",
        property: "숙소",
        contact: "연락처",
        bookPackage: "패키지 예약"
      },
      hero: {
        title: "공간을 통한 치유 – 모든 순간을 만지다",
        subtitle: "집 같은 편안함",
        description: "내면의 평화와 연결을 찾는 이들을 위한 치유와 감정적 요소가 있는 웰니스 리트리트 공간.",
        bookNow: "지금 패키지 예약"
      },
      packages: {
        title: "맞춤형 체험 패키지",
        spa: {
          title: "스파 & 건강 치료",
          description: "전문적인 스파 트리트먼트로 휴식과 에너지 회복"
        },
        culture: {
          title: "현지 문화 체험",
          description: "독특한 현지 문화를 발견하고 체험하세요"
        },
        emotional: {
          title: "감정 지원 – 내면 여행",
          description: "균형을 찾고 자신과 다시 연결하세요"
        }
      },
      about: {
        title: "우리의 이야기",
        story: "송 홈스테이는 다낭에서 응우옌 팟 씨에 의해 설립되었습니다. 그는 집처럼 가까우면서도 바다 자연과 조화로운 숙박 공간을 만들고자 하는 열망을 가지고 있었습니다. 우리는 각 홈스테이가 단순한 휴식 장소가 아니라 감정적 경험의 여정이자 여러분의 \"두 번째 집\"이라고 믿습니다.",
        currentProject: "현재 프로젝트",
        projectName: "송 – 웰니스 리트리트",
        projectDescription: "내면의 평화와 연결을 찾는 이들을 위한 치유와 감정적 요소가 있는 리트리트 공간."
      },
      property: {
        title: "우리의 숙소",
        moreInfo: "자세한 정보",
        bookOn: "예약하기"
      },
      contact: {
        title: "연락처",
        phone: "전화번호",
        email: "이메일",
        social: "소셜 미디어"
      }
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'vi',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;
