import { useTranslation } from "react-i18next";
import { motion, useScroll, useTransform } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Waves, Heart, Sparkles } from "lucide-react";
import { useRef } from "react";

const PackagesSection = () => {
  const { t } = useTranslation();
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);

  const packages = [
    {
      icon: <Waves className="h-8 w-8" />,
      title: t("packages.spa.title"),
      description: t("packages.spa.description"),
      image:
        "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
      number: "01",
    },
    {
      icon: <Sparkles className="h-8 w-8" />,
      title: t("packages.culture.title"),
      description: t("packages.culture.description"),
      image:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
      number: "02",
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: t("packages.emotional.title"),
      description: t("packages.emotional.description"),
      image:
        "https://images.unsplash.com/photo-1545389336-cf090694435e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
      number: "03",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section
      ref={ref}
      className="py-32 px-6 bg-gray-50 relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <motion.div
        style={{ y, opacity }}
        className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full blur-3xl"
      />
      <motion.div
        style={{ y: useTransform(scrollYProgress, [0, 1], [-50, 50]), opacity }}
        className="absolute bottom-20 left-10 w-24 h-24 bg-gradient-to-br from-green-100 to-green-200 rounded-full blur-2xl"
      />

      <div className="container mx-auto relative">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.p
            className="text-sm uppercase tracking-wider mb-4"
            style={{ color: "var(--color-text-secondary)" }}
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            TRẢI NGHIỆM ĐẶC BIỆT
          </motion.p>

          <motion.h2
            className="text-4xl md:text-5xl font-light mb-6 leading-tight"
            style={{ color: "var(--color-text)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {t("packages.title")}
          </motion.h2>

          <motion.div
            className="w-16 h-0.5 mx-auto"
            style={{ backgroundColor: "var(--color-primary)" }}
            initial={{ width: 0 }}
            whileInView={{ width: 64 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          />
        </motion.div>

        <motion.div
          className="grid lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {packages.map((pkg, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{
                y: -10,
                transition: { duration: 0.3 },
              }}
              className="group cursor-pointer"
            >
              <div className="bg-white shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden">
                <div className="relative h-80 overflow-hidden">
                  <motion.img
                    src={pkg.image}
                    alt={pkg.title}
                    className="w-full h-full object-cover"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.6 }}
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>

                  {/* Package number */}
                  <div className="absolute top-6 left-6">
                    <span className="text-6xl font-light text-white/80">
                      {pkg.number}
                    </span>
                  </div>

                  {/* Icon */}
                  <motion.div
                    className="absolute bottom-6 right-6 p-3 rounded-full backdrop-blur-sm"
                    style={{ backgroundColor: "rgba(255,255,255,0.2)" }}
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div style={{ color: "white" }}>{pkg.icon}</div>
                  </motion.div>
                </div>

                <div className="p-8">
                  <motion.h3
                    className="text-xl font-medium mb-4 leading-tight"
                    style={{ color: "var(--color-text)" }}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    {pkg.title}
                  </motion.h3>

                  <motion.p
                    className="text-sm leading-relaxed mb-6"
                    style={{ color: "var(--color-text-secondary)" }}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    {pkg.description}
                  </motion.p>

                  <motion.div
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Button
                      variant="outline"
                      className="text-sm uppercase tracking-wider border-gray-300 hover:border-gray-900 transition-all duration-300"
                      style={{ color: "var(--color-text)" }}
                    >
                      {t("nav.bookPackage")}
                    </Button>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default PackagesSection;
