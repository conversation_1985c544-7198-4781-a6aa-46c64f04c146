import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { Waves, Heart, Sparkles } from 'lucide-react';

const PackagesSection = () => {
  const { t } = useTranslation();

  const packages = [
    {
      icon: <Waves className="h-12 w-12" />,
      title: t('packages.spa.title'),
      description: t('packages.spa.description'),
      image: '/placeholder.svg'
    },
    {
      icon: <Sparkles className="h-12 w-12" />,
      title: t('packages.culture.title'),
      description: t('packages.culture.description'),
      image: '/placeholder.svg'
    },
    {
      icon: <Heart className="h-12 w-12" />,
      title: t('packages.emotional.title'),
      description: t('packages.emotional.description'),
      image: '/placeholder.svg'
    }
  ];

  return (
    <section className="py-20 px-6" style={{ backgroundColor: 'var(--color-accent)' }}>
      <div className="container mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{ color: 'var(--color-text)' }}>
            {t('packages.title')}
          </h2>
          <div className="w-24 h-1 mx-auto mb-8" style={{ backgroundColor: 'var(--color-primary)' }}></div>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {packages.map((pkg, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="bg-white rounded-lg shadow-lg overflow-hidden group cursor-pointer"
            >
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={pkg.image} 
                  alt={pkg.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <div 
                  className="absolute top-4 left-4 p-3 rounded-full"
                  style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}
                >
                  {pkg.icon}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                  {pkg.title}
                </h3>
                <p className="mb-6" style={{ color: 'var(--color-text-secondary)' }}>
                  {pkg.description}
                </p>
                <Button 
                  className="w-full text-white hover:opacity-90"
                  style={{ backgroundColor: 'var(--color-primary)' }}
                >
                  {t('nav.bookPackage')}
                </Button>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Button 
            size="lg"
            className="px-12 py-4 text-lg font-semibold text-white hover:opacity-90"
            style={{ backgroundColor: 'var(--color-secondary)' }}
          >
            Xem tất cả gói trải nghiệm
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default PackagesSection;
