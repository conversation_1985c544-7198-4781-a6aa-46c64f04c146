import { useTranslation } from "react-i18next";
import { motion, useScroll, useTransform } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useRef } from "react";

const AboutSection = () => {
  const { t } = useTranslation();
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);

  return (
    <section ref={ref} className="py-32 px-6 relative overflow-hidden">
      {/* Background decorative elements */}
      <motion.div
        style={{ y }}
        className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full blur-3xl opacity-60"
      />
      <motion.div
        style={{ y: useTransform(scrollYProgress, [0, 1], [-30, 30]) }}
        className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-br from-green-50 to-green-100 rounded-full blur-2xl opacity-60"
      />

      <div className="container mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.p
              className="text-sm uppercase tracking-wider mb-4"
              style={{ color: "var(--color-text-secondary)" }}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              VỀ CHÚNG TÔI
            </motion.p>

            <motion.h2
              className="text-4xl md:text-5xl font-light mb-6 leading-tight"
              style={{ color: "var(--color-text)" }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {t("about.title")}
            </motion.h2>

            <motion.div
              className="w-16 h-0.5 mb-8"
              style={{ backgroundColor: "var(--color-primary)" }}
              initial={{ width: 0 }}
              whileInView={{ width: 64 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            />

            <motion.p
              className="text-lg leading-relaxed mb-8"
              style={{ color: "var(--color-text-secondary)" }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              {t("about.story")}
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <Button
                variant="outline"
                className="text-sm uppercase tracking-wider border-gray-300 hover:border-gray-900 transition-all duration-300 hover:scale-105"
                style={{ color: "var(--color-text)" }}
              >
                Tìm hiểu thêm
              </Button>
            </motion.div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <motion.div
              style={{ scale }}
              className="relative overflow-hidden shadow-2xl"
            >
              <img
                src="https://images.unsplash.com/photo-1600298881974-6be191ceeda1?w=800&h=600&fit=crop"
                alt="Nguyen Phat - Founder"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </motion.div>

            {/* Floating quote */}
            <motion.div
              className="absolute -bottom-6 -left-6 bg-white p-6 shadow-xl max-w-xs"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1, duration: 0.6 }}
              whileHover={{ scale: 1.05 }}
            >
              <p
                className="text-sm italic mb-2"
                style={{ color: "var(--color-text-secondary)" }}
              >
                "Chữa lành từ không gian – chạm trong từng khoảnh khắc"
              </p>
              <p
                className="text-xs font-medium"
                style={{ color: "var(--color-primary)" }}
              >
                - Nguyễn Phát, Founder
              </p>
            </motion.div>
          </motion.div>
        </div>

        {/* Current Project Section */}
        <motion.div
          className="mt-32 text-center"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.p
            className="text-sm uppercase tracking-wider mb-4"
            style={{ color: "var(--color-text-secondary)" }}
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {t("about.currentProject")}
          </motion.p>

          <motion.h3
            className="text-3xl md:text-4xl font-light mb-6"
            style={{ color: "var(--color-primary)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {t("about.projectName")}
          </motion.h3>

          <motion.p
            className="text-lg max-w-2xl mx-auto leading-relaxed"
            style={{ color: "var(--color-text-secondary)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {t("about.projectDescription")}
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
