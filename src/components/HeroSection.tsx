import { Button } from "@/components/ui/button";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`
        }}
      >
        <div className="absolute inset-0 bg-black/30"></div>
      </div>
      
      {/* Content */}
      <div className="relative container mx-auto px-6 text-white">
        <div className="max-w-2xl">
          <p className="text-sm uppercase tracking-wider mb-4 text-white/90">
            OUR STUNNING VILLAS
          </p>
          
          <h1 className="text-5xl md:text-6xl font-light mb-6 leading-tight">
            INVEST IN YOUR DREAM
            <br />
            <span className="font-normal">TODAY</span>
          </h1>
          
          <div className="w-16 h-0.5 bg-white mb-8"></div>
          
          <p className="text-lg mb-8 text-white/90 max-w-lg leading-relaxed">
            With breathtaking views and top-of-the-line amenities, our properties provide the perfect escape for those seeking relaxation and adventure.
          </p>
          
          <Button className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm uppercase tracking-wider">
            OUR PROPERTIES
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;