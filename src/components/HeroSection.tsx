import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

const HeroSection = () => {
  const { t } = useTranslation();

  return (
    <section className="relative min-h-screen flex items-center">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
        }}
      >
        <div className="absolute inset-0 bg-black/30"></div>
      </div>

      {/* Content */}
      <div className="relative container mx-auto px-6 text-white">
        <div className="max-w-2xl">
          <motion.p
            className="text-sm uppercase tracking-wider mb-4 text-white/90"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            SÓNG HOMESTAY
          </motion.p>

          <motion.h1
            className="text-5xl md:text-6xl font-light mb-6 leading-tight"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {t("hero.title")}
            <br />
            <span className="font-normal">{t("hero.subtitle")}</span>
          </motion.h1>

          <motion.div
            className="w-16 h-0.5 bg-white mb-8"
            initial={{ width: 0 }}
            animate={{ width: 64 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          ></motion.div>

          <motion.p
            className="text-lg mb-8 text-white/90 max-w-lg leading-relaxed"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {t("hero.description")}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <Button
              className="text-white hover:bg-gray-800 px-8 py-3 text-sm uppercase tracking-wider transition-all duration-300 hover:scale-105"
              style={{ backgroundColor: "var(--color-primary)" }}
            >
              {t("hero.bookNow")}
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Floating elements for animation */}
      <motion.div
        className="absolute top-1/4 right-1/4 w-2 h-2 bg-white/20 rounded-full"
        animate={{
          y: [0, -20, 0],
          opacity: [0.2, 0.8, 0.2],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          delay: 0.5,
        }}
      />
      <motion.div
        className="absolute top-1/3 right-1/3 w-1 h-1 bg-white/30 rounded-full"
        animate={{
          y: [0, -15, 0],
          opacity: [0.3, 0.9, 0.3],
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          delay: 1.2,
        }}
      />
      <motion.div
        className="absolute bottom-1/3 right-1/5 w-3 h-3 bg-white/10 rounded-full"
        animate={{
          y: [0, -25, 0],
          opacity: [0.1, 0.6, 0.1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          delay: 2,
        }}
      />
    </section>
  );
};

export default HeroSection;
