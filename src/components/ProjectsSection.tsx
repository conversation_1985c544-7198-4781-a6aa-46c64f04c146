import { Badge } from "@/components/ui/badge";

const projects = [
  {
    id: 1,
    title: "KENKO BALI PRIVATE RESIDENCES",
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    status: null
  },
  {
    id: 2,
    title: "WELLNESS STUDIO",
    image: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    status: null
  },
  {
    id: 3,
    title: "KENKO BALI BINGIN SUITES",
    image: "https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    status: "SOLD OUT"
  },
  {
    id: 4,
    title: "THE SOHO LOFT",
    image: "https://images.unsplash.com/photo-1565182999561-18d7dc61c393?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    status: "SOLD OUT"
  }
];

const ProjectsSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light mb-8 text-gray-900">
            Our Projects
          </h2>
          
          <h3 className="text-2xl md:text-3xl font-light mb-8 text-gray-700">
            Luxury Villas For Sale In Bingin Beach, Bali
          </h3>
          
          <div className="max-w-4xl mx-auto mb-12">
            <img 
              src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
              alt="Bingin Beach Villa"
              className="w-full h-80 object-cover rounded-lg shadow-xl"
            />
          </div>
          
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            The villas are located within minutes to some of Bali's best beaches including Bingin, Dreamland and Padang Padang. 
            This is an excellent choice for anyone who is looking for a home or investment in Bukit, Bingin area and close to all the attractions that the area offers.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 mt-16">
          {projects.map((project) => (
            <div key={project.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-lg shadow-lg">
                <img 
                  src={project.image}
                  alt={project.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {project.status && (
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-red-500 text-white text-xs px-3 py-1">
                      {project.status}
                    </Badge>
                  </div>
                )}
              </div>
              <h4 className="text-xl font-medium mt-4 text-gray-900">
                {project.title}
              </h4>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;